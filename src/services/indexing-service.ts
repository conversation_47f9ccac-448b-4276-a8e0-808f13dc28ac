/**
 * Core Codebase Indexing Service
 * Provides intelligent indexing and querying of Swift/Objective-C codebases
 */

import * as fs from "fs/promises";
import * as path from "path";
import { EventEmitter } from "events";
import {
  CodeSymbol,
  CodeRelationship,
  SymbolQuery,
  ParseResult,
  ProjectContext,
} from "../types/index.js";
import { XcodeServerError } from "../utils/core/errors.js";
import { CacheService } from "./cache-service.js";
import { PathService } from "./path-service.js";

/**
 * Indexing configuration options
 */
export interface IndexingConfig {
  maxFileSize: number; // Maximum file size to parse (bytes)
  excludePatterns: string[]; // Glob patterns to exclude
  includePatterns: string[]; // Glob patterns to include
  enableRealTimeUpdates: boolean;
  cacheEnabled: boolean;
  maxCacheSize: number;
  indexingTimeout: number; // Timeout for indexing operations (ms)
}

/**
 * Indexing statistics
 */
export interface IndexingStats {
  totalFiles: number;
  indexedFiles: number;
  skippedFiles: number;
  errorFiles: number;
  totalSymbols: number;
  totalRelationships: number;
  indexingTime: number;
  lastIndexed: Date;
}

/**
 * Language parser interface
 */
export interface LanguageParser {
  canParse(filePath: string): boolean;
  parseFile(filePath: string, content: string): Promise<ParseResult>;
  getLanguage(): string;
}

/**
 * Core codebase indexing service with enterprise-grade performance and reliability
 */
export class CodebaseIndexingService extends EventEmitter {
  private symbolIndex = new Map<string, CodeSymbol>();
  private relationshipIndex = new Map<string, CodeRelationship[]>();
  private fileIndex = new Map<string, string[]>(); // file -> symbol IDs
  private reverseIndex = new Map<string, Set<string>>(); // symbol name -> symbol IDs
  private parsers = new Map<string, LanguageParser>();
  private isIndexing = false;
  private indexingStats: IndexingStats;
  private config: IndexingConfig;

  constructor(
    private cacheService: CacheService,
    private pathService: PathService,
    config: Partial<IndexingConfig> = {}
  ) {
    super();

    this.config = {
      maxFileSize: 1024 * 1024, // 1MB
      excludePatterns: [
        "**/node_modules/**",
        "**/build/**",
        "**/DerivedData/**",
        "**/.git/**",
        "**/Pods/**",
        "**/*.xcassets/**",
        "**/*.bundle/**",
      ],
      includePatterns: ["**/*.swift", "**/*.m", "**/*.mm", "**/*.h"],
      enableRealTimeUpdates: true,
      cacheEnabled: true,
      maxCacheSize: 10000,
      indexingTimeout: 300000, // 5 minutes
      ...config,
    };

    this.indexingStats = {
      totalFiles: 0,
      indexedFiles: 0,
      skippedFiles: 0,
      errorFiles: 0,
      totalSymbols: 0,
      totalRelationships: 0,
      indexingTime: 0,
      lastIndexed: new Date(0),
    };
  }

  /**
   * Register a language parser
   */
  registerParser(parser: LanguageParser): void {
    this.parsers.set(parser.getLanguage(), parser);
    console.log(`Registered parser for ${parser.getLanguage()}`);
  }

  /**
   * Index a project directory
   */
  async indexProject(projectPath: string): Promise<IndexingStats> {
    if (this.isIndexing) {
      throw new XcodeServerError("Indexing already in progress");
    }

    this.isIndexing = true;
    const startTime = Date.now();

    try {
      console.log(`Starting indexing of project: ${projectPath}`);
      this.emit("indexingStarted", { projectPath });

      // Clear existing index
      this.clearIndex();

      // Get all files to index
      const files = await this.getFilesToIndex(projectPath);
      this.indexingStats.totalFiles = files.length;

      console.log(`Found ${files.length} files to index`);

      // Index files in batches for better performance
      const batchSize = 10;
      for (let i = 0; i < files.length; i += batchSize) {
        const batch = files.slice(i, i + batchSize);
        await Promise.all(batch.map((file) => this.indexFile(file)));

        // Emit progress
        this.emit("indexingProgress", {
          processed: Math.min(i + batchSize, files.length),
          total: files.length,
          percentage: Math.round(
            (Math.min(i + batchSize, files.length) / files.length) * 100
          ),
        });
      }

      // Build reverse index for fast symbol lookup
      this.buildReverseIndex();

      // Update statistics
      this.indexingStats.indexingTime = Date.now() - startTime;
      this.indexingStats.lastIndexed = new Date();
      this.indexingStats.totalSymbols = this.symbolIndex.size;
      this.indexingStats.totalRelationships = Array.from(
        this.relationshipIndex.values()
      ).reduce((sum, rels) => sum + rels.length, 0);

      console.log(`Indexing completed in ${this.indexingStats.indexingTime}ms`);
      console.log(
        `Indexed ${this.indexingStats.indexedFiles} files, ${this.indexingStats.totalSymbols} symbols`
      );

      this.emit("indexingCompleted", this.indexingStats);
      return this.indexingStats;
    } catch (error) {
      this.emit("indexingError", error);
      throw new XcodeServerError(
        `Failed to index project: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      this.isIndexing = false;
    }
  }

  /**
   * Query symbols with advanced filtering
   */
  async querySymbols(query: SymbolQuery): Promise<CodeSymbol[]> {
    const cacheKey = `symbols:${JSON.stringify(query)}`;

    if (this.config.cacheEnabled) {
      const cached = this.cacheService.get(cacheKey) as
        | CodeSymbol[]
        | undefined;
      if (cached) {
        return cached;
      }
    }

    let results: CodeSymbol[] = [];

    // Start with all symbols or filter by name
    if (query.name) {
      const symbolIds = query.fuzzyMatch
        ? this.fuzzySearchSymbols(query.name)
        : this.reverseIndex.get(query.name) || new Set();

      results = Array.from(symbolIds)
        .map((id) => this.symbolIndex.get(id))
        .filter((symbol): symbol is CodeSymbol => symbol !== undefined);
    } else {
      results = Array.from(this.symbolIndex.values());
    }

    // Apply filters
    if (query.type && query.type.length > 0) {
      results = results.filter((symbol) => query.type!.includes(symbol.type));
    }

    if (query.file) {
      results = results.filter((symbol) => symbol.file.includes(query.file!));
    }

    if (query.accessibility && query.accessibility.length > 0) {
      results = results.filter((symbol) =>
        query.accessibility!.includes(symbol.accessibility)
      );
    }

    if (query.hasDocumentation !== undefined) {
      results = results.filter((symbol) =>
        query.hasDocumentation ? !!symbol.documentation : !symbol.documentation
      );
    }

    if (query.parentSymbol) {
      results = results.filter(
        (symbol) => symbol.parentSymbol === query.parentSymbol
      );
    }

    // Apply pagination
    const offset = query.offset || 0;
    const limit = query.limit || 100;
    results = results.slice(offset, offset + limit);

    // Cache results
    if (this.config.cacheEnabled) {
      this.cacheService.set(cacheKey, results, { ttl: 300000 }); // 5 minutes
    }

    return results;
  }

  /**
   * Get related symbols through relationship graph
   */
  async getRelatedSymbols(
    symbolId: string,
    depth: number = 2
  ): Promise<CodeSymbol[]> {
    const visited = new Set<string>();
    const queue: Array<{ id: string; currentDepth: number }> = [
      { id: symbolId, currentDepth: 0 },
    ];
    const results: CodeSymbol[] = [];

    while (queue.length > 0) {
      const { id, currentDepth } = queue.shift()!;

      if (visited.has(id) || currentDepth > depth) {
        continue;
      }

      visited.add(id);
      const symbol = this.symbolIndex.get(id);
      if (symbol && id !== symbolId) {
        results.push(symbol);
      }

      // Add related symbols to queue
      const relationships = this.relationshipIndex.get(id) || [];
      for (const rel of relationships) {
        if (!visited.has(rel.to)) {
          queue.push({ id: rel.to, currentDepth: currentDepth + 1 });
        }
      }
    }

    return results;
  }

  /**
   * Get indexing statistics
   */
  getIndexingStats(): IndexingStats {
    return { ...this.indexingStats };
  }

  /**
   * Check if indexing is in progress
   */
  isIndexingInProgress(): boolean {
    return this.isIndexing;
  }

  /**
   * Clear the entire index
   */
  clearIndex(): void {
    this.symbolIndex.clear();
    this.relationshipIndex.clear();
    this.fileIndex.clear();
    this.reverseIndex.clear();

    this.indexingStats = {
      totalFiles: 0,
      indexedFiles: 0,
      skippedFiles: 0,
      errorFiles: 0,
      totalSymbols: 0,
      totalRelationships: 0,
      indexingTime: 0,
      lastIndexed: new Date(0),
    };
  }

  /**
   * Get files to index based on configuration
   */
  private async getFilesToIndex(projectPath: string): Promise<string[]> {
    const files: string[] = [];

    const processDirectory = async (dirPath: string): Promise<void> => {
      try {
        const entries = await fs.readdir(dirPath, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dirPath, entry.name);

          if (entry.isDirectory()) {
            // Check if directory should be excluded
            if (!this.shouldExcludePath(fullPath)) {
              await processDirectory(fullPath);
            }
          } else if (entry.isFile()) {
            // Check if file should be included
            if (this.shouldIncludeFile(fullPath)) {
              files.push(fullPath);
            }
          }
        }
      } catch (error) {
        console.warn(`Failed to read directory ${dirPath}:`, error);
      }
    };

    await processDirectory(projectPath);
    return files;
  }

  /**
   * Check if a path should be excluded
   */
  private shouldExcludePath(filePath: string): boolean {
    return this.config.excludePatterns.some((pattern) =>
      this.matchesPattern(filePath, pattern)
    );
  }

  /**
   * Check if a file should be included
   */
  private shouldIncludeFile(filePath: string): boolean {
    if (this.shouldExcludePath(filePath)) {
      return false;
    }

    return this.config.includePatterns.some((pattern) =>
      this.matchesPattern(filePath, pattern)
    );
  }

  /**
   * Simple glob pattern matching
   */
  private matchesPattern(filePath: string, pattern: string): boolean {
    // Convert glob pattern to regex
    const regexPattern = pattern
      .replace(/\*\*/g, ".*")
      .replace(/\*/g, "[^/]*")
      .replace(/\?/g, "[^/]");

    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(filePath);
  }

  /**
   * Index a single file
   */
  private async indexFile(filePath: string): Promise<void> {
    try {
      // Check file size
      const stats = await fs.stat(filePath);
      if (stats.size > this.config.maxFileSize) {
        this.indexingStats.skippedFiles++;
        return;
      }

      // Find appropriate parser
      const parser = this.findParser(filePath);
      if (!parser) {
        this.indexingStats.skippedFiles++;
        return;
      }

      // Read and parse file
      const content = await fs.readFile(filePath, "utf-8");
      const parseResult = await parser.parseFile(filePath, content);

      // Store symbols
      for (const symbol of parseResult.symbols) {
        this.symbolIndex.set(symbol.id, symbol);

        // Update file index
        if (!this.fileIndex.has(filePath)) {
          this.fileIndex.set(filePath, []);
        }
        this.fileIndex.get(filePath)!.push(symbol.id);
      }

      // Store relationships
      for (const relationship of parseResult.relationships) {
        if (!this.relationshipIndex.has(relationship.from)) {
          this.relationshipIndex.set(relationship.from, []);
        }
        this.relationshipIndex.get(relationship.from)!.push(relationship);
      }

      this.indexingStats.indexedFiles++;
    } catch (error) {
      console.warn(`Failed to index file ${filePath}:`, error);
      this.indexingStats.errorFiles++;
    }
  }

  /**
   * Find appropriate parser for a file
   */
  private findParser(filePath: string): LanguageParser | undefined {
    for (const parser of this.parsers.values()) {
      if (parser.canParse(filePath)) {
        return parser;
      }
    }
    return undefined;
  }

  /**
   * Build reverse index for fast symbol name lookup
   */
  private buildReverseIndex(): void {
    this.reverseIndex.clear();

    for (const symbol of this.symbolIndex.values()) {
      if (!this.reverseIndex.has(symbol.name)) {
        this.reverseIndex.set(symbol.name, new Set());
      }
      this.reverseIndex.get(symbol.name)!.add(symbol.id);
    }
  }

  /**
   * Fuzzy search for symbols by name
   */
  private fuzzySearchSymbols(searchTerm: string): Set<string> {
    const results = new Set<string>();
    const lowerSearchTerm = searchTerm.toLowerCase();

    for (const [symbolName, symbolIds] of this.reverseIndex) {
      if (this.fuzzyMatch(symbolName.toLowerCase(), lowerSearchTerm)) {
        for (const id of symbolIds) {
          results.add(id);
        }
      }
    }

    return results;
  }

  /**
   * Simple fuzzy matching algorithm
   */
  private fuzzyMatch(text: string, pattern: string): boolean {
    let patternIndex = 0;

    for (let i = 0; i < text.length && patternIndex < pattern.length; i++) {
      if (text[i] === pattern[patternIndex]) {
        patternIndex++;
      }
    }

    return patternIndex === pattern.length;
  }
}
