/**
 * Performance Optimization Engine
 * Enhances indexing performance through lazy loading, intelligent caching,
 * background processing, and memory-efficient data structures
 */

import { EventEmitter } from "events";
import { Worker } from "worker_threads";
import * as path from "path";
import { XcodeServerError } from "../utils/core/errors.js";
import { CacheService } from "./cache-service.js";
import { CodebaseIndexingService } from "./indexing-service.js";

/**
 * Performance optimization configuration
 */
export interface PerformanceConfig {
  enableLazyLoading: boolean;
  enableBackgroundProcessing: boolean;
  enableMemoryOptimization: boolean;
  maxWorkerThreads: number;
  chunkSize: number;
  memoryThreshold: number; // MB
  cacheWarmupEnabled: boolean;
  preloadPatterns: string[];
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  indexingTime: number;
  memoryUsage: number;
  cacheHitRate: number;
  backgroundTasksCompleted: number;
  lazyLoadOperations: number;
  averageResponseTime: number;
  peakMemoryUsage: number;
  totalOptimizations: number;
}

/**
 * Background task definition
 */
export interface BackgroundTask {
  id: string;
  type: "index" | "cache-warmup" | "memory-cleanup" | "preload";
  priority: number;
  data: any;
  createdAt: Date;
  estimatedDuration: number;
}

/**
 * Lazy loading cache entry
 */
interface LazyLoadEntry<T> {
  loader: () => Promise<T>;
  loaded: boolean;
  data?: T;
  lastAccessed: Date;
  accessCount: number;
}

/**
 * Performance optimization service for enhanced indexing
 */
export class PerformanceOptimizationService extends EventEmitter {
  private config: PerformanceConfig;
  private metrics: PerformanceMetrics;
  private backgroundTasks: BackgroundTask[] = [];
  private workers: Worker[] = [];
  private lazyLoadCache = new Map<string, LazyLoadEntry<any>>();
  private memoryMonitor?: NodeJS.Timeout;
  private backgroundProcessor?: NodeJS.Timeout;
  private isProcessingBackground = false;

  constructor(
    private cacheService: CacheService,
    private indexingService: CodebaseIndexingService,
    config: Partial<PerformanceConfig> = {}
  ) {
    super();

    this.config = {
      enableLazyLoading: true,
      enableBackgroundProcessing: true,
      enableMemoryOptimization: true,
      maxWorkerThreads: Math.max(
        1,
        Math.floor(require("os").cpus().length / 2)
      ),
      chunkSize: 50,
      memoryThreshold: 512, // 512MB
      cacheWarmupEnabled: true,
      preloadPatterns: ["**/*.swift", "**/Package.swift", "**/project.pbxproj"],
      ...config,
    };

    this.metrics = {
      indexingTime: 0,
      memoryUsage: 0,
      cacheHitRate: 0,
      backgroundTasksCompleted: 0,
      lazyLoadOperations: 0,
      averageResponseTime: 0,
      peakMemoryUsage: 0,
      totalOptimizations: 0,
    };

    this.initialize();
  }

  /**
   * Initialize the performance optimization service
   */
  private async initialize(): Promise<void> {
    if (this.config.enableMemoryOptimization) {
      this.startMemoryMonitoring();
    }

    if (this.config.enableBackgroundProcessing) {
      this.startBackgroundProcessor();
    }

    if (this.config.cacheWarmupEnabled) {
      this.scheduleBackgroundTask({
        id: `cache-warmup-${Date.now()}`,
        type: "cache-warmup",
        priority: 3,
        data: { patterns: this.config.preloadPatterns },
        createdAt: new Date(),
        estimatedDuration: 5000,
      });
    }
  }

  /**
   * Optimize indexing operation with performance enhancements
   */
  async optimizeIndexing(filePaths: string[]): Promise<void> {
    const startTime = Date.now();

    try {
      if (
        this.config.enableBackgroundProcessing &&
        filePaths.length > this.config.chunkSize
      ) {
        await this.processInBackground(filePaths);
      } else {
        await this.processInChunks(filePaths);
      }

      this.metrics.indexingTime = Date.now() - startTime;
      this.metrics.totalOptimizations++;

      this.emit("indexingOptimized", {
        fileCount: filePaths.length,
        duration: this.metrics.indexingTime,
      });
    } catch (error) {
      this.emit("optimizationError", { error, filePaths });
      throw error;
    }
  }

  /**
   * Create lazy loader for expensive operations
   */
  createLazyLoader<T>(key: string, loader: () => Promise<T>): () => Promise<T> {
    this.lazyLoadCache.set(key, {
      loader,
      loaded: false,
      lastAccessed: new Date(),
      accessCount: 0,
    });

    return async (): Promise<T> => {
      const entry = this.lazyLoadCache.get(key);
      if (!entry) {
        throw new XcodeServerError(
          `Lazy loader not found: ${key}`,
          { key }
        );
      }

      entry.lastAccessed = new Date();
      entry.accessCount++;

      if (!entry.loaded) {
        entry.data = await entry.loader();
        entry.loaded = true;
        this.metrics.lazyLoadOperations++;
      }

      return entry.data!;
    };
  }

  /**
   * Preload frequently accessed data
   */
  async preloadData(patterns: string[]): Promise<void> {
    this.scheduleBackgroundTask({
      id: `preload-${Date.now()}`,
      type: "preload",
      priority: 2,
      data: { patterns },
      createdAt: new Date(),
      estimatedDuration: 3000,
    });
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    this.updateMemoryMetrics();
    this.updateCacheMetrics();
    return { ...this.metrics };
  }

  /**
   * Schedule a background task
   */
  scheduleBackgroundTask(task: BackgroundTask): void {
    this.backgroundTasks.push(task);
    this.backgroundTasks.sort((a, b) => b.priority - a.priority);

    this.emit("taskScheduled", task);
  }

  /**
   * Process files in optimized chunks
   */
  private async processInChunks(filePaths: string[]): Promise<void> {
    const chunks = this.createChunks(filePaths, this.config.chunkSize);

    for (const chunk of chunks) {
      await Promise.all(
        chunk.map((filePath) => this.indexingService.indexFile(filePath))
      );

      // Check memory usage between chunks
      if (this.config.enableMemoryOptimization) {
        await this.checkMemoryUsage();
      }
    }
  }

  /**
   * Process files in background using worker threads
   */
  private async processInBackground(filePaths: string[]): Promise<void> {
    const chunks = this.createChunks(filePaths, this.config.chunkSize);
    const workerPromises: Promise<void>[] = [];

    for (
      let i = 0;
      i < Math.min(chunks.length, this.config.maxWorkerThreads);
      i++
    ) {
      const chunk = chunks[i];
      if (chunk && chunk.length > 0) {
        workerPromises.push(this.processChunkInWorker(chunk));
      }
    }

    await Promise.all(workerPromises);
  }

  /**
   * Process a chunk of files in a worker thread
   */
  private async processChunkInWorker(filePaths: string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      const workerPath = path.join(__dirname, "../workers/indexing-worker.js");
      const worker = new Worker(workerPath, {
        workerData: { filePaths },
      });

      worker.on("message", (result) => {
        if (result.success) {
          resolve();
        } else {
          reject(new Error(result.error));
        }
      });

      worker.on("error", reject);
      worker.on("exit", (code) => {
        if (code !== 0) {
          reject(new Error(`Worker stopped with exit code ${code}`));
        }
      });

      this.workers.push(worker);
    });
  }

  /**
   * Create optimized chunks from file paths
   */
  private createChunks<T>(items: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < items.length; i += chunkSize) {
      chunks.push(items.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    this.memoryMonitor = setInterval(() => {
      this.checkMemoryUsage();
    }, 30000); // Check every 30 seconds
  }

  /**
   * Check memory usage and optimize if needed
   */
  private async checkMemoryUsage(): Promise<void> {
    const memoryUsage = process.memoryUsage();
    const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;

    this.metrics.memoryUsage = heapUsedMB;
    this.metrics.peakMemoryUsage = Math.max(
      this.metrics.peakMemoryUsage,
      heapUsedMB
    );

    if (heapUsedMB > this.config.memoryThreshold) {
      await this.performMemoryOptimization();
    }
  }

  /**
   * Perform memory optimization
   */
  private async performMemoryOptimization(): Promise<void> {
    // Clear old lazy load entries
    const now = new Date();
    for (const [key, entry] of this.lazyLoadCache.entries()) {
      const ageMinutes =
        (now.getTime() - entry.lastAccessed.getTime()) / (1000 * 60);
      if (ageMinutes > 30 && entry.accessCount < 3) {
        this.lazyLoadCache.delete(key);
      }
    }

    // Clear cache entries
    this.cacheService.clearExpired();

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    this.emit("memoryOptimized", {
      beforeMB: this.metrics.memoryUsage,
      afterMB: process.memoryUsage().heapUsed / 1024 / 1024,
    });
  }

  /**
   * Start background task processor
   */
  private startBackgroundProcessor(): void {
    this.backgroundProcessor = setInterval(() => {
      if (!this.isProcessingBackground && this.backgroundTasks.length > 0) {
        this.processNextBackgroundTask();
      }
    }, 1000);
  }

  /**
   * Process next background task
   */
  private async processNextBackgroundTask(): Promise<void> {
    if (this.isProcessingBackground || this.backgroundTasks.length === 0) {
      return;
    }

    this.isProcessingBackground = true;
    const task = this.backgroundTasks.shift()!;
    const startTime = Date.now();

    try {
      await this.executeBackgroundTask(task);

      this.metrics.backgroundTasksCompleted++;
      const duration = Date.now() - startTime;

      this.emit("backgroundTaskCompleted", { task, duration });
    } catch (error) {
      this.emit("backgroundTaskError", { task, error });
    } finally {
      this.isProcessingBackground = false;
    }
  }

  /**
   * Execute a background task
   */
  private async executeBackgroundTask(task: BackgroundTask): Promise<void> {
    switch (task.type) {
      case "cache-warmup":
        await this.performCacheWarmup(task.data.patterns);
        break;
      case "preload":
        await this.performPreload(task.data.patterns);
        break;
      case "memory-cleanup":
        await this.performMemoryOptimization();
        break;
      case "index":
        await this.indexingService.indexFile(task.data.filePath);
        break;
      default:
        throw new Error(`Unknown background task type: ${task.type}`);
    }
  }

  /**
   * Perform cache warmup
   */
  private async performCacheWarmup(patterns: string[]): Promise<void> {
    // Warm up frequently accessed cache entries
    const commonKeys = [
      "project:active",
      "project:configuration",
      "symbols:recent",
      "files:swift",
    ];

    for (const key of commonKeys) {
      try {
        // Pre-populate cache with common queries
        await this.cacheService.getOrSet(key, async () => {
          // Return placeholder data for warmup
          return { warmedUp: true, timestamp: new Date() };
        });
      } catch (error) {
        // Ignore warmup errors
      }
    }
  }

  /**
   * Perform data preloading
   */
  private async performPreload(patterns: string[]): Promise<void> {
    // Preload commonly accessed files and symbols
    for (const pattern of patterns) {
      try {
        // Create lazy loaders for pattern-matched files
        const key = `preload:${pattern}`;
        this.createLazyLoader(key, async () => {
          // Return preloaded data structure
          return { pattern, preloaded: true, timestamp: new Date() };
        });
      } catch (error) {
        // Ignore preload errors
      }
    }
  }

  /**
   * Update memory metrics
   */
  private updateMemoryMetrics(): void {
    const memoryUsage = process.memoryUsage();
    this.metrics.memoryUsage = memoryUsage.heapUsed / 1024 / 1024;
    this.metrics.peakMemoryUsage = Math.max(
      this.metrics.peakMemoryUsage,
      this.metrics.memoryUsage
    );
  }

  /**
   * Update cache metrics
   */
  private updateCacheMetrics(): void {
    const cacheMetrics = this.cacheService.getMetrics();
    if (cacheMetrics.gets > 0) {
      this.metrics.cacheHitRate = (cacheMetrics.hits / cacheMetrics.gets) * 100;
    }
  }

  /**
   * Optimize response time for operations
   */
  async optimizeResponseTime<T>(operation: () => Promise<T>): Promise<T> {
    const startTime = Date.now();

    try {
      const result = await operation();

      const responseTime = Date.now() - startTime;
      this.updateAverageResponseTime(responseTime);

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateAverageResponseTime(responseTime);
      throw error;
    }
  }

  /**
   * Update average response time
   */
  private updateAverageResponseTime(newTime: number): void {
    if (this.metrics.averageResponseTime === 0) {
      this.metrics.averageResponseTime = newTime;
    } else {
      // Use exponential moving average
      this.metrics.averageResponseTime =
        this.metrics.averageResponseTime * 0.9 + newTime * 0.1;
    }
  }

  /**
   * Get background task queue status
   */
  getBackgroundTaskStatus(): {
    queueLength: number;
    isProcessing: boolean;
    completedTasks: number;
  } {
    return {
      queueLength: this.backgroundTasks.length,
      isProcessing: this.isProcessingBackground,
      completedTasks: this.metrics.backgroundTasksCompleted,
    };
  }

  /**
   * Dispose of the performance optimization service
   */
  async dispose(): Promise<void> {
    // Clear timers
    if (this.memoryMonitor) {
      clearInterval(this.memoryMonitor);
    }
    if (this.backgroundProcessor) {
      clearInterval(this.backgroundProcessor);
    }

    // Terminate workers
    await Promise.all(this.workers.map((worker) => worker.terminate()));
    this.workers = [];

    // Clear caches
    this.lazyLoadCache.clear();
    this.backgroundTasks = [];

    this.removeAllListeners();
  }
}
