/**
 * Swift Language Parser
 * Provides basic Swift code parsing and symbol extraction
 */

import * as path from "path";
import { v4 as uuidv4 } from "uuid";
import {
  CodeSymbol,
  CodeRelationship,
  ParseResult,
  ImportStatement,
  ParseError,
  SymbolParameter,
} from "../types/index.js";
import { LanguageParser } from "./indexing-service.js";

/**
 * Basic Swift parser implementation
 * Note: This is a simplified parser for demonstration. In production,
 * you would use Swift's SourceKit or a proper AST parser.
 */
export class SwiftParser implements LanguageParser {
  private static readonly SWIFT_EXTENSIONS = [".swift"];
  
  canParse(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return SwiftParser.SWIFT_EXTENSIONS.includes(ext);
  }

  getLanguage(): string {
    return "swift";
  }

  async parseFile(filePath: string, content: string): Promise<ParseResult> {
    const startTime = Date.now();
    const symbols: CodeSymbol[] = [];
    const relationships: CodeRelationship[] = [];
    const imports: ImportStatement[] = [];
    const errors: ParseError[] = [];

    try {
      const lines = content.split('\n');
      
      // Parse imports
      this.parseImports(lines, filePath, imports);
      
      // Parse symbols
      await this.parseSymbols(lines, filePath, symbols, relationships, errors);
      
    } catch (error) {
      errors.push({
        message: `Parse error: ${error instanceof Error ? error.message : String(error)}`,
        file: filePath,
        line: 1,
        column: 1,
        severity: "error",
      });
    }

    return {
      symbols,
      relationships,
      imports,
      errors,
      metadata: {
        parseTime: Date.now() - startTime,
        fileSize: content.length,
        language: "swift",
      },
    };
  }

  /**
   * Parse import statements
   */
  private parseImports(lines: string[], filePath: string, imports: ImportStatement[]): void {
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const importMatch = line.match(/^import\s+(@testable\s+)?(@_implementationOnly\s+)?(\w+)/);
      
      if (importMatch) {
        imports.push({
          module: importMatch[3],
          isTestable: !!importMatch[1],
          isImplementationOnly: !!importMatch[2],
          file: filePath,
          line: i + 1,
        });
      }
    }
  }

  /**
   * Parse Swift symbols (classes, structs, enums, functions, etc.)
   */
  private async parseSymbols(
    lines: string[],
    filePath: string,
    symbols: CodeSymbol[],
    relationships: CodeRelationship[],
    errors: ParseError[]
  ): Promise<void> {
    let currentClass: CodeSymbol | null = null;
    let braceDepth = 0;
    let inMultilineComment = false;

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i];
      const originalLine = line;
      const lineNumber = i + 1;

      // Handle multiline comments
      if (line.includes("/*")) {
        inMultilineComment = true;
      }
      if (line.includes("*/")) {
        inMultilineComment = false;
        continue;
      }
      if (inMultilineComment) {
        continue;
      }

      // Remove single-line comments
      const commentIndex = line.indexOf("//");
      if (commentIndex !== -1) {
        line = line.substring(0, commentIndex);
      }

      line = line.trim();
      if (!line) continue;

      // Track brace depth
      braceDepth += (line.match(/{/g) || []).length;
      braceDepth -= (line.match(/}/g) || []).length;

      try {
        // Parse class/struct/enum declarations
        const typeMatch = line.match(/^(public|private|internal|fileprivate|open)?\s*(final\s+)?(class|struct|enum|protocol|extension)\s+(\w+)(?:\s*:\s*([^{]+))?/);
        if (typeMatch) {
          const symbol = this.createTypeSymbol(typeMatch, filePath, lineNumber, originalLine);
          symbols.push(symbol);
          currentClass = symbol;

          // Parse inheritance/protocol conformance
          if (typeMatch[5]) {
            this.parseInheritance(symbol, typeMatch[5], relationships, filePath, lineNumber);
          }
          continue;
        }

        // Parse function declarations
        const funcMatch = line.match(/^(public|private|internal|fileprivate|open)?\s*(static\s+)?(func\s+)(\w+)\s*\(([^)]*)\)(?:\s*(?:async\s+)?(?:throws\s+)?->\s*([^{]+))?/);
        if (funcMatch) {
          const symbol = this.createFunctionSymbol(funcMatch, filePath, lineNumber, originalLine, currentClass);
          symbols.push(symbol);
          continue;
        }

        // Parse property declarations
        const propMatch = line.match(/^(public|private|internal|fileprivate|open)?\s*(static\s+)?(let|var)\s+(\w+)\s*:\s*([^=\s{]+)/);
        if (propMatch) {
          const symbol = this.createPropertySymbol(propMatch, filePath, lineNumber, originalLine, currentClass);
          symbols.push(symbol);
          continue;
        }

        // Reset current class when exiting its scope
        if (currentClass && braceDepth === 0) {
          currentClass = null;
        }

      } catch (error) {
        errors.push({
          message: `Error parsing line: ${error instanceof Error ? error.message : String(error)}`,
          file: filePath,
          line: lineNumber,
          column: 1,
          severity: "warning",
        });
      }
    }
  }

  /**
   * Create a type symbol (class, struct, enum, protocol)
   */
  private createTypeSymbol(
    match: RegExpMatchArray,
    filePath: string,
    lineNumber: number,
    originalLine: string
  ): CodeSymbol {
    const accessibility = (match[1] as CodeSymbol['accessibility']) || 'internal';
    const isFinal = !!match[2];
    const type = match[3] as CodeSymbol['type'];
    const name = match[4];

    return {
      id: uuidv4(),
      name,
      type,
      file: filePath,
      line: lineNumber,
      column: originalLine.indexOf(name) + 1,
      accessibility,
      signature: originalLine.trim(),
      parentSymbol: undefined,
      childSymbols: [],
      attributes: isFinal ? ['final'] : [],
      protocols: [],
      metadata: {
        isFinal,
        originalLine,
      },
    };
  }

  /**
   * Create a function symbol
   */
  private createFunctionSymbol(
    match: RegExpMatchArray,
    filePath: string,
    lineNumber: number,
    originalLine: string,
    parentClass: CodeSymbol | null
  ): CodeSymbol {
    const accessibility = (match[1] as CodeSymbol['accessibility']) || 'internal';
    const isStatic = !!match[2];
    const name = match[4];
    const parametersStr = match[5] || '';
    const returnType = match[6]?.trim();

    const parameters = this.parseParameters(parametersStr);

    const symbol: CodeSymbol = {
      id: uuidv4(),
      name,
      type: 'function',
      file: filePath,
      line: lineNumber,
      column: originalLine.indexOf(name) + 1,
      accessibility,
      signature: originalLine.trim(),
      parentSymbol: parentClass?.id,
      childSymbols: [],
      attributes: [],
      isStatic,
      isAsync: originalLine.includes('async'),
      isThrows: originalLine.includes('throws'),
      returnType,
      parameters,
      metadata: {
        originalLine,
      },
    };

    // Add to parent's children
    if (parentClass) {
      parentClass.childSymbols.push(symbol.id);
    }

    return symbol;
  }

  /**
   * Create a property symbol
   */
  private createPropertySymbol(
    match: RegExpMatchArray,
    filePath: string,
    lineNumber: number,
    originalLine: string,
    parentClass: CodeSymbol | null
  ): CodeSymbol {
    const accessibility = (match[1] as CodeSymbol['accessibility']) || 'internal';
    const isStatic = !!match[2];
    const isLet = match[3] === 'let';
    const name = match[4];
    const type = match[5];

    const symbol: CodeSymbol = {
      id: uuidv4(),
      name,
      type: 'property',
      file: filePath,
      line: lineNumber,
      column: originalLine.indexOf(name) + 1,
      accessibility,
      signature: originalLine.trim(),
      parentSymbol: parentClass?.id,
      childSymbols: [],
      attributes: isLet ? ['let'] : ['var'],
      isStatic,
      returnType: type,
      metadata: {
        isLet,
        originalLine,
      },
    };

    // Add to parent's children
    if (parentClass) {
      parentClass.childSymbols.push(symbol.id);
    }

    return symbol;
  }

  /**
   * Parse function parameters
   */
  private parseParameters(parametersStr: string): SymbolParameter[] {
    if (!parametersStr.trim()) {
      return [];
    }

    const parameters: SymbolParameter[] = [];
    const paramParts = parametersStr.split(',');

    for (const part of paramParts) {
      const trimmed = part.trim();
      if (!trimmed) continue;

      // Parse parameter: externalName internalName: Type = defaultValue
      const paramMatch = trimmed.match(/^(?:(\w+)\s+)?(\w+)\s*:\s*([^=]+)(?:\s*=\s*(.+))?/);
      if (paramMatch) {
        parameters.push({
          name: paramMatch[2],
          externalName: paramMatch[1],
          type: paramMatch[3].trim(),
          defaultValue: paramMatch[4]?.trim(),
          isVariadic: trimmed.includes('...'),
          isInout: trimmed.includes('inout'),
        });
      }
    }

    return parameters;
  }

  /**
   * Parse inheritance and protocol conformance
   */
  private parseInheritance(
    symbol: CodeSymbol,
    inheritanceStr: string,
    relationships: CodeRelationship[],
    filePath: string,
    lineNumber: number
  ): void {
    const parts = inheritanceStr.split(',').map(s => s.trim());
    
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      if (!part) continue;

      const relationshipType = i === 0 && symbol.type === 'class' ? 'inheritance' : 'protocol_conformance';
      
      relationships.push({
        id: uuidv4(),
        from: symbol.id,
        to: part, // This would be resolved to actual symbol ID in a full implementation
        type: relationshipType,
        strength: relationshipType === 'inheritance' ? 10 : 7,
        file: filePath,
        line: lineNumber,
        metadata: {
          inheritanceString: part,
        },
      });

      if (relationshipType === 'inheritance') {
        symbol.superclass = part;
      } else {
        symbol.protocols = symbol.protocols || [];
        symbol.protocols.push(part);
      }
    }
  }
}
