/**
 * Professional Code Analysis MCP Tools
 * Provides comprehensive Swift/SwiftUI code analysis, error detection, and quality assessment
 */

import { z } from "zod";
import { XcodeServer } from "../server.js";
import { getGlobalContainer } from "../services/service-container.js";

/**
 * Register all code analysis tools (6 tools)
 * Includes: analyze_code_quality, scan_for_errors, get_error_report,
 * resolve_error, get_code_metrics, analyze_project_errors
 */
export function registerCodeAnalysisTools(server: XcodeServer) {
  // 1. analyze_code_quality
  server.server.tool(
    "analyze_code_quality",
    "Perform comprehensive code quality analysis on Swift/SwiftUI files with syntax error detection, performance issues, and best practices validation",
    {
      filePath: z.string().describe("Path to the Swift file to analyze"),
      includeContext: z
        .boolean()
        .optional()
        .describe("Include code context around errors (default: true)"),
      enableRealTimeScanning: z
        .boolean()
        .optional()
        .describe(
          "Enable real-time file watching for continuous analysis (default: false)"
        ),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const codeAnalysisService = await container.resolveAsync(
          "codeAnalysis"
        );

        const result = await (codeAnalysisService as any).analyzeFile(
          args.filePath
        );

        return {
          content: [
            {
              type: "text",
              text: `Code Quality Analysis for ${result.file}:

📊 **Analysis Summary**:
  • Total Issues: ${result.summary.totalIssues}
  • Critical Issues: ${result.summary.criticalIssues}
  • High Priority Issues: ${result.summary.highPriorityIssues}
  • Analysis Time: ${result.summary.analysisTime}ms

📈 **Code Metrics**:
  • Lines of Code: ${result.metrics.linesOfCode}
  • Complexity Score: ${result.metrics.complexity}
  • Maintainability Index: ${result.metrics.maintainabilityIndex.toFixed(1)}

🚨 **Errors Found** (${result.errors.length}):
${result.errors
  .slice(0, 5)
  .map(
    (error: any, i: number) => `
${i + 1}. **${error.title}** (${error.severity})
   📁 Line ${error.line}:${error.column}
   📝 ${error.description}
   ${error.suggestedFix ? `💡 Fix: ${error.suggestedFix.description}` : ""}
   ${
     args.includeContext
       ? `
   📋 Context:
   ${error.context.beforeLines
     .map(
       (line: string, idx: number) =>
         `   ${error.line - error.context.beforeLines.length + idx}: ${line}`
     )
     .join("\n")}
   ➤  ${error.line}: ${error.context.errorLine}
   ${error.context.afterLines
     .map((line: string, idx: number) => `   ${error.line + idx + 1}: ${line}`)
     .join("\n")}`
       : ""
   }
`
  )
  .join("")}

⚠️ **Warnings Found** (${result.warnings.length}):
${result.warnings
  .slice(0, 3)
  .map(
    (warning: any, i: number) => `
${i + 1}. **${warning.title}** (${warning.severity})
   📁 Line ${warning.line}:${warning.column} - ${warning.description}
`
  )
  .join("")}

${
  result.errors.length > 5
    ? `\n... and ${result.errors.length - 5} more errors`
    : ""
}
${
  result.warnings.length > 3
    ? `\n... and ${result.warnings.length - 3} more warnings`
    : ""
}

💡 **Quality Assessment**: ${
                result.summary.criticalIssues === 0 &&
                result.summary.highPriorityIssues === 0
                  ? "Good code quality"
                  : result.summary.criticalIssues > 0
                  ? "Critical issues require immediate attention"
                  : "Some improvements recommended"
              }`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error analyzing code quality: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 2. scan_for_errors
  server.server.tool(
    "scan_for_errors",
    "Real-time error scanning with Swift compiler integration and structured error reporting",
    {
      projectPath: z.string().describe("Path to the project to scan"),
      filePattern: z
        .string()
        .optional()
        .describe(
          "File pattern to match (e.g., '*.swift', default: all Swift files)"
        ),
      severity: z
        .enum(["critical", "high", "medium", "low"])
        .optional()
        .describe("Minimum severity level to report"),
      enableXcodeBuild: z
        .boolean()
        .optional()
        .describe(
          "Enable Xcode build system integration for compilation errors"
        ),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const codeAnalysisService = await container.resolveAsync(
          "codeAnalysis"
        );

        const results = await (codeAnalysisService as any).analyzeProject(
          args.projectPath
        );

        // Filter by severity if specified
        let allErrors: any[] = [];
        let allWarnings: any[] = [];

        for (const result of results) {
          allErrors.push(...result.errors);
          allWarnings.push(...result.warnings);
        }

        if (args.severity) {
          const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          const minLevel = severityOrder[args.severity];
          allErrors = allErrors.filter(
            (e) =>
              severityOrder[e.severity as keyof typeof severityOrder] >=
              minLevel
          );
          allWarnings = allWarnings.filter(
            (w) =>
              severityOrder[w.severity as keyof typeof severityOrder] >=
              minLevel
          );
        }

        const criticalErrors = allErrors.filter(
          (e) => e.severity === "critical"
        );
        const highErrors = allErrors.filter((e) => e.severity === "high");

        return {
          content: [
            {
              type: "text",
              text: `Error Scan Results for ${args.projectPath}:

🔍 **Scan Summary**:
  • Files Analyzed: ${results.length}
  • Total Errors: ${allErrors.length}
  • Total Warnings: ${allWarnings.length}
  • Critical Issues: ${criticalErrors.length}
  • High Priority Issues: ${highErrors.length}

${
  criticalErrors.length > 0
    ? `
🚨 **CRITICAL ERRORS** (${criticalErrors.length}):
${criticalErrors
  .slice(0, 3)
  .map(
    (error: any, i: number) => `
${i + 1}. **${error.title}**
   📁 ${error.file}:${error.line}:${error.column}
   📝 ${error.description}
   🔧 Rule: ${error.rule || "N/A"}
   ${error.suggestedFix ? `💡 ${error.suggestedFix.description}` : ""}
`
  )
  .join("")}
`
    : ""
}

${
  highErrors.length > 0
    ? `
⚠️ **HIGH PRIORITY ERRORS** (${highErrors.length}):
${highErrors
  .slice(0, 3)
  .map(
    (error: any, i: number) => `
${i + 1}. **${error.title}**
   📁 ${error.file}:${error.line}:${error.column}
   📝 ${error.description}
`
  )
  .join("")}
`
    : ""
}

📊 **Error Categories**:
${Object.entries(
  allErrors.reduce((acc: any, error: any) => {
    acc[error.category] = (acc[error.category] || 0) + 1;
    return acc;
  }, {})
)
  .map(([category, count]) => `  • ${category}: ${count}`)
  .join("\n")}

🎯 **Recommendations**:
${
  criticalErrors.length > 0
    ? "  • Address critical security and syntax errors immediately"
    : ""
}
${
  highErrors.length > 5
    ? "  • Consider code review for high-priority issues"
    : ""
}
${
  allErrors.length > 50
    ? "  • Implement automated code quality checks in CI/CD"
    : ""
}
${
  allErrors.length === 0 ? "  • Excellent! No errors found in the codebase" : ""
}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error scanning for issues: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 3. get_error_report
  server.server.tool(
    "get_error_report",
    "Generate comprehensive error report with trend analysis and AI-friendly context",
    {
      projectPath: z.string().describe("Path to the project"),
      format: z
        .enum(["json", "html", "csv"])
        .optional()
        .describe("Report format (default: json)"),
      includeResolved: z
        .boolean()
        .optional()
        .describe("Include resolved errors in the report"),
      timeRange: z
        .enum(["24h", "7d", "30d", "all"])
        .optional()
        .describe("Time range for error data (default: 7d)"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const errorReportingService = await container.resolveAsync(
          "errorReporting"
        );

        // Set up filter based on time range
        const filter: any = {};
        if (args.timeRange && args.timeRange !== "all") {
          const timeRanges = {
            "24h": 24 * 60 * 60 * 1000,
            "7d": 7 * 24 * 60 * 60 * 1000,
            "30d": 30 * 24 * 60 * 60 * 1000,
          };
          filter.dateFrom = new Date(Date.now() - timeRanges[args.timeRange]);
        }

        if (args.includeResolved !== undefined) {
          filter.resolved = args.includeResolved;
        }

        const report = await (errorReportingService as any).generateReport(
          args.projectPath,
          args.format || "json"
        );

        const stats = (errorReportingService as any).getStatistics();

        return {
          content: [
            {
              type: "text",
              text:
                args.format === "json"
                  ? `Error Report Generated:

📊 **Report Summary**:
  • Project: ${args.projectPath}
  • Format: ${args.format || "json"}
  • Time Range: ${args.timeRange || "7d"}
  • Total Errors: ${stats.totalErrors}
  • Resolved Errors: ${stats.resolvedErrors}
  • Resolution Rate: ${stats.resolutionRate.toFixed(1)}%

📈 **Statistics**:
  • Average Resolution Time: ${(
    stats.averageResolutionTime /
    (1000 * 60 * 60)
  ).toFixed(1)} hours
  • Top Error Files: ${stats.topErrorFiles
    .slice(0, 3)
    .map((f: any) => `${f.file} (${f.count})`)
    .join(", ")}

📋 **Report Content**:
${
  args.format === "json"
    ? "JSON report with detailed error data, trends, and recommendations"
    : args.format === "html"
    ? "HTML report with visual charts and formatted tables"
    : "CSV report with tabular error data for analysis"
}

${
  args.format === "json"
    ? `\n📄 **JSON Report**:\n${report}`
    : `\n📄 **Report Generated**: ${(
        args.format || "json"
      ).toUpperCase()} format ready`
}`
                  : report,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error generating report: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 4. resolve_error
  server.server.tool(
    "resolve_error",
    "Mark an error as resolved with optional notes and resolution details",
    {
      errorId: z.string().describe("ID of the error to resolve"),
      resolvedBy: z
        .string()
        .optional()
        .describe("Name or identifier of who resolved the error"),
      notes: z
        .string()
        .optional()
        .describe("Notes about how the error was resolved"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const errorReportingService = await container.resolveAsync(
          "errorReporting"
        );

        const success = await (errorReportingService as any).resolveError(
          args.errorId,
          args.resolvedBy,
          args.notes
        );

        if (success) {
          return {
            content: [
              {
                type: "text",
                text: `✅ Error Resolved Successfully:

🔧 **Error ID**: ${args.errorId}
👤 **Resolved By**: ${args.resolvedBy || "Unknown"}
📝 **Notes**: ${args.notes || "No additional notes"}
⏰ **Resolved At**: ${new Date().toISOString()}

The error has been marked as resolved and will be excluded from future active error reports.`,
              },
            ],
          };
        } else {
          return {
            content: [
              {
                type: "text",
                text: `❌ Error not found: ${args.errorId}

The specified error ID could not be found in the error log. Please verify the error ID and try again.`,
              },
            ],
            isError: true,
          };
        }
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error resolving error: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 5. get_code_metrics
  server.server.tool(
    "get_code_metrics",
    "Get detailed code metrics and quality indicators for files or projects",
    {
      path: z.string().describe("Path to file or project to analyze"),
      includeComplexity: z
        .boolean()
        .optional()
        .describe("Include detailed complexity analysis"),
      includeTrends: z
        .boolean()
        .optional()
        .describe("Include historical trend data"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const codeAnalysisService = await container.resolveAsync(
          "codeAnalysis"
        );

        // Check if path is a file or directory
        const isFile =
          args.path.endsWith(".swift") || args.path.endsWith(".swiftui");

        if (isFile) {
          const result = await (codeAnalysisService as any).analyzeFile(
            args.path
          );

          return {
            content: [
              {
                type: "text",
                text: `Code Metrics for ${result.file}:

📊 **Basic Metrics**:
  • Lines of Code: ${result.metrics.linesOfCode}
  • Complexity Score: ${result.metrics.complexity}
  • Maintainability Index: ${result.metrics.maintainabilityIndex.toFixed(1)}/100

${
  args.includeComplexity
    ? `
🧮 **Complexity Analysis**:
  • Cyclomatic Complexity: ${result.metrics.complexity}
  • Complexity Rating: ${
    result.metrics.complexity <= 5
      ? "Low (Good)"
      : result.metrics.complexity <= 10
      ? "Medium (Acceptable)"
      : result.metrics.complexity <= 20
      ? "High (Consider Refactoring)"
      : "Very High (Refactor Required)"
  }
  • Maintainability: ${
    result.metrics.maintainabilityIndex >= 80
      ? "Excellent"
      : result.metrics.maintainabilityIndex >= 60
      ? "Good"
      : result.metrics.maintainabilityIndex >= 40
      ? "Fair"
      : "Poor"
  }
`
    : ""
}

🎯 **Quality Indicators**:
  • Error Density: ${(
    (result.errors.length / result.metrics.linesOfCode) *
    100
  ).toFixed(2)} errors per 100 LOC
  • Warning Density: ${(
    (result.warnings.length / result.metrics.linesOfCode) *
    100
  ).toFixed(2)} warnings per 100 LOC
  • Overall Quality: ${
    result.summary.criticalIssues === 0 &&
    result.metrics.maintainabilityIndex >= 70
      ? "High"
      : result.summary.criticalIssues === 0 &&
        result.metrics.maintainabilityIndex >= 50
      ? "Medium"
      : "Low"
  }

💡 **Recommendations**:
${
  result.metrics.complexity > 15
    ? "  • Consider breaking down complex functions"
    : ""
}
${
  result.metrics.maintainabilityIndex < 50
    ? "  • Improve code organization and documentation"
    : ""
}
${
  result.errors.length > 0
    ? "  • Address code quality issues"
    : "  • Code quality is good"
}`,
              },
            ],
          };
        } else {
          // Project-level metrics
          const results = await (codeAnalysisService as any).analyzeProject(
            args.path
          );
          const stats = (codeAnalysisService as any).getAnalysisStats();

          const totalLOC = results.reduce(
            (sum: number, r: any) => sum + r.metrics.linesOfCode,
            0
          );
          const avgComplexity =
            results.reduce(
              (sum: number, r: any) => sum + r.metrics.complexity,
              0
            ) / results.length;
          const avgMaintainability =
            results.reduce(
              (sum: number, r: any) => sum + r.metrics.maintainabilityIndex,
              0
            ) / results.length;

          return {
            content: [
              {
                type: "text",
                text: `Project Code Metrics for ${args.path}:

📊 **Project Overview**:
  • Total Files: ${results.length}
  • Total Lines of Code: ${totalLOC.toLocaleString()}
  • Average File Size: ${Math.round(totalLOC / results.length)} LOC
  • Total Errors: ${stats.totalErrors}
  • Total Warnings: ${stats.totalWarnings}

📈 **Quality Metrics**:
  • Average Complexity: ${avgComplexity.toFixed(1)}
  • Average Maintainability: ${avgMaintainability.toFixed(1)}/100
  • Error Rate: ${((stats.totalErrors / totalLOC) * 1000).toFixed(
    2
  )} errors per 1000 LOC
  • Analysis Performance: ${stats.averageAnalysisTime.toFixed(1)}ms per file

🎯 **Project Health**:
  • Overall Rating: ${
    stats.totalErrors === 0 && avgMaintainability >= 70
      ? "Excellent"
      : stats.totalErrors < 10 && avgMaintainability >= 60
      ? "Good"
      : stats.totalErrors < 50 && avgMaintainability >= 40
      ? "Fair"
      : "Needs Improvement"
  }
  • Code Quality Trend: ${
    stats.totalErrors === 0
      ? "Stable (No errors)"
      : stats.totalErrors < results.length * 2
      ? "Good (Low error density)"
      : "Declining (High error density)"
  }

💡 **Project Recommendations**:
${
  avgComplexity > 10
    ? "  • Focus on reducing complexity in high-complexity files"
    : ""
}
${avgMaintainability < 60 ? "  • Improve overall code maintainability" : ""}
${stats.totalErrors > 20 ? "  • Implement automated code quality checks" : ""}
${
  stats.totalErrors === 0
    ? "  • Excellent code quality - maintain current standards"
    : ""
}`,
              },
            ],
          };
        }
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error getting code metrics: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 6. analyze_project_errors
  server.server.tool(
    "analyze_project_errors",
    "Comprehensive project-wide error analysis with aggregation and trend insights",
    {
      projectPath: z.string().describe("Path to the project to analyze"),
      groupBy: z
        .enum(["file", "category", "severity", "rule"])
        .optional()
        .describe("Group errors by specific criteria"),
      includeContext: z
        .boolean()
        .optional()
        .describe("Include AI-friendly error context for assistance"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const errorReportingService = await container.resolveAsync(
          "errorReporting"
        );

        const aggregation = (
          errorReportingService as any
        ).getErrorAggregation();
        const stats = (errorReportingService as any).getStatistics();

        return {
          content: [
            {
              type: "text",
              text: `Project Error Analysis for ${args.projectPath}:

📊 **Error Overview**:
  • Total Errors: ${aggregation.totalErrors}
  • Critical: ${aggregation.errorsBySeverity.critical}
  • High Priority: ${aggregation.errorsBySeverity.high}
  • Medium Priority: ${aggregation.errorsBySeverity.medium}
  • Low Priority: ${aggregation.errorsBySeverity.low}

📈 **Error Distribution**:
${
  args.groupBy === "category" || !args.groupBy
    ? `
**By Category**:
${Object.entries(aggregation.errorsByCategory)
  .sort(([, a], [, b]) => (b as number) - (a as number))
  .map(([category, count]) => `  • ${category}: ${count}`)
  .join("\n")}
`
    : ""
}

${
  args.groupBy === "severity" || !args.groupBy
    ? `
**By Severity**:
${Object.entries(aggregation.errorsBySeverity)
  .sort(([, a], [, b]) => (b as number) - (a as number))
  .map(([severity, count]) => `  • ${severity}: ${count}`)
  .join("\n")}
`
    : ""
}

${
  args.groupBy === "file" || !args.groupBy
    ? `
**Top Error Files**:
${aggregation.topFiles
  .slice(0, 5)
  .map((file: any, i: number) => `${i + 1}. ${file.file}: ${file.count} errors`)
  .join("\n")}
`
    : ""
}

${
  args.groupBy === "rule" || !args.groupBy
    ? `
**Top Error Rules**:
${aggregation.topRules
  .slice(0, 5)
  .map(
    (rule: any, i: number) =>
      `${i + 1}. ${rule.rule}: ${rule.count} occurrences`
  )
  .join("\n")}
`
    : ""
}

📊 **Project Statistics**:
  • Resolution Rate: ${stats.resolutionRate.toFixed(1)}%
  • Average Resolution Time: ${(
    stats.averageResolutionTime /
    (1000 * 60 * 60)
  ).toFixed(1)} hours
  • Errors per Day (Recent): ${
    stats.errorsByDay
      .slice(-7)
      .reduce((sum: number, day: any) => sum + day.count, 0) / 7
  } avg

🎯 **Priority Actions**:
${
  aggregation.errorsBySeverity.critical > 0
    ? "  🚨 Address critical errors immediately"
    : ""
}
${
  aggregation.errorsBySeverity.high > 10
    ? "  ⚠️ High priority errors need attention"
    : ""
}
${
  aggregation.topFiles.length > 0 && aggregation.topFiles[0].count > 10
    ? `  📁 Focus on ${aggregation.topFiles[0].file} (${aggregation.topFiles[0].count} errors)`
    : ""
}
${stats.resolutionRate < 50 ? "  📈 Improve error resolution process" : ""}

${
  args.includeContext
    ? `
🤖 **AI Assistant Context**:
The project has ${aggregation.totalErrors} total errors with ${
        aggregation.errorsBySeverity.critical
      } critical issues requiring immediate attention.
The most problematic areas are ${aggregation.topFiles
        .slice(0, 2)
        .map((f: any) => f.file)
        .join(" and ")} with the highest error counts.
Common issues include ${Object.entries(aggregation.errorsByCategory)
        .sort(([, a], [, b]) => (b as number) - (a as number))
        .slice(0, 2)
        .map(([cat]) => cat)
        .join(" and ")} problems.
Resolution rate is ${stats.resolutionRate.toFixed(1)}% which is ${
        stats.resolutionRate >= 70
          ? "good"
          : stats.resolutionRate >= 50
          ? "acceptable"
          : "concerning"
      }.
`
    : ""
}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error analyzing project errors: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );
}
